[package]
name = "citrea-evm"
authors = { workspace = true }
edition = { workspace = true }
homepage = { workspace = true }
license = { workspace = true }
repository = { workspace = true }
description = "EVM Module of Citrea"

version = { workspace = true }
publish = false
readme = "README.md"

[dependencies]
short-header-proof-provider = { path = "../short-header-proof-provider", default-features = false }
sov-db = { path = "../sovereign-sdk/full-node/db/sov-db", optional = true }
sov-modules-api = { path = "../sovereign-sdk/module-system/sov-modules-api", default-features = false, features = ["macros"] }
sov-rollup-interface = { path = "../sovereign-sdk/rollup-interface" }
sov-state = { path = "../sovereign-sdk/module-system/sov-state" }

citrea-primitives = { path = "../primitives" }

borsh = { workspace = true, features = ["rc"] }
hex = { workspace = true, optional = true }
jsonrpsee = { workspace = true, features = ["macros", "client-core", "server"], optional = true }
serde = { workspace = true }
serde_json = { workspace = true, optional = true }
sha2 = { workspace = true }
thiserror = { workspace = true }
tracing = { workspace = true, optional = true }

alloy-consensus = { workspace = true }
alloy-eips = { workspace = true }
alloy-network = { workspace = true, optional = true }
alloy-primitives = { workspace = true }
alloy-rlp = { workspace = true }
alloy-rpc-types = { workspace = true, optional = true }
alloy-rpc-types-eth = { workspace = true, optional = true }
alloy-rpc-types-trace = { workspace = true, optional = true }
alloy-serde = { workspace = true, optional = true }
alloy-sol-types = { workspace = true }
itertools = { workspace = true, optional = true }
k256 = { workspace = true, features = ["schnorr"] }
reth-primitives = { workspace = true, default-features = false }
reth-primitives-traits = { workspace = true, default-features = false }
reth-provider = { workspace = true, optional = true }
reth-rpc = { workspace = true, optional = true }
reth-rpc-eth-api = { workspace = true, optional = true }
reth-rpc-eth-types = { workspace = true, optional = true }
reth-rpc-server-types = { workspace = true, optional = true }
reth-rpc-types-compat = { workspace = true, optional = true }
reth-transaction-pool = { workspace = true, optional = true }
revm = { workspace = true, default-features = false }
revm-inspectors = { workspace = true, optional = true, features = ["js-tracer"] }
revm-precompile = { workspace = true }

[dev-dependencies]
alloy = { workspace = true, features = ["consensus", "providers", "signers", "signer-local"] }
alloy-eips = { workspace = true }
bcs = { workspace = true }
bytes = { workspace = true }
citrea-primitives = { path = "../primitives", features = ["testing"] }
hex = { workspace = true }
rand = { workspace = true }
rayon = { workspace = true }
reth-chainspec = { workspace = true }
reth-db = { workspace = true }
reth-errors = { workspace = true }
reth-rpc-eth-types = { workspace = true }
rstest = "0.22.0"
secp256k1 = { workspace = true }
sha2 = { workspace = true }
sov-keys = { path = "../sovereign-sdk/module-system/sov-keys" }
sov-modules-api = { path = "../sovereign-sdk/module-system/sov-modules-api", features = ["macros"] }
sov-prover-storage-manager = { path = "../sovereign-sdk/full-node/sov-prover-storage-manager", features = ["test-utils"] }
sov-rollup-interface = { path = "../sovereign-sdk/rollup-interface", features = ["testing"] }
tempfile = { workspace = true }
tracing-subscriber = { workspace = true }
walkdir = "2.3.3"

[features]
default = []
native = [
  "dep:sov-db",
  "sov-state/native",
  "sov-modules-api/native",

  "dep:reth-rpc",
  "dep:reth-rpc-eth-types",
  "dep:reth-rpc-eth-api",
  "dep:reth-rpc-server-types",
  "dep:reth-rpc-types-compat",
  "revm/serde",
  "dep:reth-transaction-pool",
  "dep:revm-inspectors",
  "dep:reth-provider",
  "dep:alloy-rpc-types",
  "dep:alloy-rpc-types-eth",
  "dep:alloy-rpc-types-trace",
  "dep:alloy-serde",
  "dep:alloy-network",

  "dep:hex",
  "dep:jsonrpsee",
  "dep:itertools",
  "dep:serde_json",
  "dep:tracing",
]
